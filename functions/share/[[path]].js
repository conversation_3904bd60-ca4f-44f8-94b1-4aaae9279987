/**
 * Cloudflare Pages Function to proxy beautiful share URLs to S3 files
 * Handles routes like:
 * - /share/github/username -> shares/github-username-latest.html
 * - /share/scholar/scholarId -> shares/scholar-scholarId-latest.html
 * - /share/github_compare/user1/user2 -> shares/github-compare-user1-user2-latest.html
 * - /share/scholar_compare/user1/user2 -> shares/scholar-compare-user1-user2-latest.html
 */

export async function onRequest(context) {
  const { request } = context
  const url = new URL(request.url)
  const pathSegments = url.pathname.split('/').filter(Boolean)
  
  // Remove 'share' from the beginning
  if (pathSegments[0] === 'share') {
    pathSegments.shift()
  }
  
  if (pathSegments.length === 0) {
    return new Response('Not Found', { status: 404 })
  }
  
  const pageType = pathSegments[0]
  let s3Key = ''
  
  try {
    // Clean identifier function (same as frontend)
    function cleanIdentifier(id) {
      return id.replace(/[^a-zA-Z0-9-_]/g, '-').replace(/-+/g, '-').replace(/^-|-$/g, '')
    }
    
    if (pageType === 'github' && pathSegments.length === 2) {
      // /share/github/username
      const username = cleanIdentifier(pathSegments[1])
      s3Key = `shares/github-${username}-latest.html`
    } else if (pageType === 'scholar' && pathSegments.length === 2) {
      // /share/scholar/scholarId
      const scholarId = cleanIdentifier(pathSegments[1])
      s3Key = `shares/scholar-${scholarId}-latest.html`
    } else if (pageType === 'github_compare' && pathSegments.length === 3) {
      // /share/github_compare/user1/user2
      const user1 = cleanIdentifier(pathSegments[1])
      const user2 = cleanIdentifier(pathSegments[2])
      // Sort to ensure consistency
      const sortedUsers = [user1, user2].sort()
      s3Key = `shares/github-compare-${sortedUsers[0]}-${sortedUsers[1]}-latest.html`
    } else if (pageType === 'scholar_compare' && pathSegments.length === 3) {
      // /share/scholar_compare/user1/user2
      const user1 = cleanIdentifier(pathSegments[1])
      const user2 = cleanIdentifier(pathSegments[2])
      // Sort to ensure consistency
      const sortedUsers = [user1, user2].sort()
      s3Key = `shares/scholar-compare-${sortedUsers[0]}-${sortedUsers[1]}-latest.html`
    } else {
      return new Response('Invalid share URL format', { status: 400 })
    }
    
    // Construct S3 URL
    const s3Url = `https://dinq-share-og.s3.us-east-2.amazonaws.com/${s3Key}`
    
    console.log('Proxying share URL:', url.pathname)
    console.log('To S3 key:', s3Key)
    console.log('S3 URL:', s3Url)
    
    // Fetch from S3
    const s3Response = await fetch(s3Url)
    
    if (!s3Response.ok) {
      console.error('S3 fetch failed:', s3Response.status, s3Response.statusText)
      return new Response('Share content not found', { status: 404 })
    }
    
    // Get the content
    const content = await s3Response.text()
    
    // Return with proper headers
    return new Response(content, {
      status: 200,
      headers: {
        'Content-Type': 'text/html',
        'Cache-Control': 'public, max-age=300', // 5 minutes cache
        'Access-Control-Allow-Origin': '*',
      }
    })
    
  } catch (error) {
    console.error('Error in share proxy:', error)
    return new Response('Internal Server Error', { status: 500 })
  }
}
