import {ref} from 'vue'
/**
 *  全局 主题数据值
 *  @class light | dark
 */ 
export const GLOBAL_CURRENT_THEME =ref('light');

export const formatThousand = (value: number | string): string => {
  if (value === null || value === undefined || value === '') return '0'

  const num = Number(value)
  if (isNaN(num)) return String(value)

  return num.toLocaleString('en-US')
}

export const convertToLabelValueArray = <T extends Record<string, any>>(
  obj: T,
  keys: Array<keyof T>
): Array<{ label: string; value: T[keyof T] }> => {
  return keys.map((key) => {
    const labelText = String(key)
      .replace(/([A-Z])/g, ' $1')
      .replace(/^./, (str) => str.toUpperCase())
      .trim()
    return {
      label: labelText,
      value: obj[key],
    }
  })
}

/**
 * 从GitHub链接或用户名中提取用户名
 * 支持的格式：
 * - username
 * - github.com/username
 * - www.github.com/username
 * - https://github.com/username
 * - https://www.github.com/username
 */
export const extractGitHubUsername = (input: string): string => {
  if (!input || typeof input !== 'string') return ''
  
  const trimmedInput = input.trim()
  
  // 如果输入不包含github.com，直接返回（假设是用户名）
  if (!trimmedInput.toLowerCase().includes('github.com')) {
    return trimmedInput
  }
  
  // 匹配GitHub URL中的用户名
  // 支持: github.com/username, www.github.com/username, https://github.com/username 等
  const githubUrlPattern = /(?:https?:\/\/)?(?:www\.)?github\.com\/([^\/\?\s]+)/i
  const match = trimmedInput.match(githubUrlPattern)
  
  if (match && match[1]) {
    return match[1]
  }
  
  // 如果没有匹配到，返回原始输入
  return trimmedInput
}

// S3 Upload Utility Functions for Share Card System

/**
 * Interface for S3 presigned URL response
 */
interface PresignedUrlResponse {
  uploadUrl: string
  publicUrl: string
}

/**
 * Get presigned URL from Cloudflare Worker
 */
export async function getPresignedUrl(fileType: 'image/png' | 'text/html', fileName?: string): Promise<PresignedUrlResponse> {
  const workerUrl = 'https://get-s3-url.pjharvey071.workers.dev'

  try {
    console.log(`Requesting presigned URL for ${fileType} from ${workerUrl}`)

    const requestBody = { fileType }
    if (fileName) {
      requestBody.fileName = fileName
    }

    const response = await fetch(workerUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestBody),
    })

    console.log('Worker response status:', response.status)
    console.log('Worker response headers:', Object.fromEntries(response.headers.entries()))

    if (!response.ok) {
      const errorText = await response.text().catch(() => 'Unable to read error response')
      console.error('Worker request failed:', response.status, errorText)
      throw new Error(`Failed to get presigned URL: ${response.status} ${response.statusText} - ${errorText}`)
    }

    const result = await response.json()
    console.log('Worker response data:', result)
    return result
  } catch (error) {
    console.error('Error getting presigned URL:', error)
    throw error
  }
}

/**
 * Upload file to S3 using presigned URL
 */
export async function uploadFileToS3(blob: Blob, fileType: 'image/png' | 'text/html', fileName?: string): Promise<string> {
  try {
    console.log(`Starting upload for ${fileType}, blob size: ${blob.size} bytes`)

    // Get presigned URL
    const { uploadUrl, publicUrl } = await getPresignedUrl(fileType, fileName)
    console.log('Got presigned URL:', uploadUrl)
    console.log('Public URL will be:', publicUrl)

    // Upload file to S3
    console.log('Starting S3 upload...')
    const uploadResponse = await fetch(uploadUrl, {
      method: 'PUT',
      headers: {
        'Content-Type': fileType,
      },
      body: blob,
    })

    console.log('Upload response status:', uploadResponse.status)
    console.log('Upload response headers:', Object.fromEntries(uploadResponse.headers.entries()))

    if (!uploadResponse.ok) {
      const errorText = await uploadResponse.text().catch(() => 'Unable to read error response')
      console.error('Upload failed with status:', uploadResponse.status)
      console.error('Error response:', errorText)
      throw new Error(`Failed to upload file to S3: ${uploadResponse.status} ${uploadResponse.statusText} - ${errorText}`)
    }

    console.log('Upload successful! Public URL:', publicUrl)
    return publicUrl
  } catch (error) {
    console.error('Upload error:', error)
    throw error
  }
}

/**
 * Generate HTML redirect page with Open Graph meta tags
 */
export function generateRedirectHtml(imageUrl: string, pageUrl: string, title: string, description: string): string {
  return `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>${title}</title>

  <!-- Open Graph meta tags for social sharing -->
  <meta property="og:title" content="${title}">
  <meta property="og:description" content="${description}">
  <meta property="og:image" content="${imageUrl}">
  <meta property="og:image:width" content="1200">
  <meta property="og:image:height" content="630">
  <meta property="og:url" content="${pageUrl}">
  <meta property="og:type" content="website">
  <meta property="og:site_name" content="DINQ">

  <!-- Twitter Card meta tags -->
  <meta name="twitter:card" content="summary_large_image">
  <meta name="twitter:title" content="${title}">
  <meta name="twitter:description" content="${description}">
  <meta name="twitter:image" content="${imageUrl}">
  <meta name="twitter:site" content="@dinq_io">

  <!-- Redirect to actual page -->
  <meta http-equiv="refresh" content="0; url=${pageUrl}">

  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      display: flex;
      align-items: center;
      justify-content: center;
      height: 100vh;
      margin: 0;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
    }
    .container {
      text-align: center;
      padding: 2rem;
    }
    .logo {
      font-size: 2rem;
      font-weight: bold;
      margin-bottom: 1rem;
    }
    .message {
      font-size: 1.1rem;
      opacity: 0.9;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="logo">DINQ</div>
    <div class="message">Redirecting to your analysis...</div>
  </div>

  <script>
    // Fallback redirect in case meta refresh doesn't work
    setTimeout(() => {
      window.location.href = '${pageUrl}';
    }, 1000);
  </script>
</body>
</html>`
}

/**
 * Complete sharing workflow: generate image, upload to S3, create HTML redirect, upload HTML
 */
/**
 * Clean and standardize identifiers for file naming
 */
export function cleanIdentifier(id: string): string {
  return id.replace(/[^a-zA-Z0-9-_]/g, '-').replace(/-+/g, '-').replace(/^-|-$/g, '')
}

/**
 * Generate standardized file name based on page type and identifiers
 */
export function generateFileName(pageType: 'github' | 'scholar' | 'github-compare' | 'scholar-compare', ...identifiers: string[]): string {
  const cleanIds = identifiers.map(id => cleanIdentifier(id))

  if (pageType.includes('compare')) {
    // Sort identifiers for comparison pages to ensure consistency
    cleanIds.sort()
    return `${pageType}-${cleanIds.join('-')}-latest.html`
  } else {
    return `${pageType}-${cleanIds[0]}-latest.html`
  }
}

/**
 * Generate beautiful share URL based on page type and identifiers
 */
export function generateShareUrl(pageType: 'github' | 'scholar' | 'github-compare' | 'scholar-compare', ...identifiers: string[]): string {
  const baseUrl = typeof window !== 'undefined' ? window.location.origin : 'https://dinq.io'

  if (pageType === 'github') {
    return `${baseUrl}/share/github/${identifiers[0]}`
  } else if (pageType === 'scholar') {
    return `${baseUrl}/share/scholar/${identifiers[0]}`
  } else if (pageType === 'github-compare') {
    return `${baseUrl}/share/github_compare/${identifiers[0]}/${identifiers[1]}`
  } else if (pageType === 'scholar-compare') {
    return `${baseUrl}/share/scholar_compare/${identifiers[0]}/${identifiers[1]}`
  }

  return `${baseUrl}/share/${pageType}/${identifiers.join('/')}`
}

export async function createShareableUrl(imageBlob: Blob, pageUrl: string, title: string, description: string, fileName?: string, shareUrl?: string): Promise<string> {
  try {
    console.log('Starting shareable URL creation workflow...')
    console.log('Image blob size:', imageBlob.size, 'bytes')
    console.log('Target page URL:', pageUrl)
    console.log('Share title:', title)

    // Generate file names if provided
    const imageFileName = fileName ? fileName.replace('.html', '.png') : undefined
    const htmlFileName = fileName

    // Step 1: Upload image to S3
    console.log('Step 1: Uploading image to S3...')
    const imageUrl = await uploadFileToS3(imageBlob, 'image/png', imageFileName)
    console.log('Step 1 complete: Image uploaded to', imageUrl)

    // Step 2: Generate HTML redirect page
    console.log('Step 2: Generating HTML redirect page...')
    const htmlContent = generateRedirectHtml(imageUrl, pageUrl, title, description)
    const htmlBlob = new Blob([htmlContent], { type: 'text/html' })
    console.log('Step 2 complete: HTML blob size:', htmlBlob.size, 'bytes')

    // Step 3: Upload HTML to S3
    console.log('Step 3: Uploading HTML to S3...')
    const s3Url = await uploadFileToS3(htmlBlob, 'text/html', htmlFileName)
    console.log('Step 3 complete: HTML uploaded to', s3Url)

    // Return beautiful share URL if provided, otherwise return S3 URL
    const finalShareUrl = shareUrl || s3Url
    console.log('Shareable URL creation workflow completed successfully!')
    console.log('Final share URL:', finalShareUrl)
    return finalShareUrl
  } catch (error) {
    console.error('Failed to create shareable URL:', error)
    throw error
  }
}
