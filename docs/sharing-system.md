# DINQ Sharing System Documentation

## Overview

The DINQ sharing system enables users to share analysis cards on social media with proper Open Graph images. The system uses a decoupled architecture with S3 storage and Cloudflare Workers for secure, scalable sharing.

## Architecture

```
User clicks Share → Generate Image → Upload to S3 → Create HTML Redirect → Upload HTML → Share on Twitter
```

### Components

1. **Frontend (Nuxt.js)**: ShareButton component handles UI and orchestrates the sharing workflow
2. **Backend (Cloudflare Worker)**: Generates S3 presigned URLs for secure uploads
3. **Storage (AWS S3)**: Stores generated images and HTML redirect pages
4. **Social Media**: Twitter displays the shared content with proper Open Graph images

## How It Works

### 1. Image Generation
- Uses `html2canvas-pro` to capture ShareCard components
- Applies styling fixes for proper rendering (SVG icons, backgrounds, themes)
- Generates high-quality PNG images (scale: 2x)

### 2. S3 Upload Workflow
- Calls Cloudflare Worker API to get presigned upload URLs
- Uploads image directly to S3 from browser
- Creates HTML redirect page with Open Graph meta tags
- Uploads HTML page to S3

### 3. Social Sharing
- Generates shareable URL pointing to HTML redirect page
- HTML page contains proper Open Graph meta tags for social media
- Redirects users to the actual analysis page after 1 second

## Usage

### Basic Usage
```vue
<ShareButton card-id="share-card" :is-dark="isDark" variant="transparent" />
```

### Props
- `cardId`: ID of the element to capture (e.g., "share-card", "share-card-github")
- `type`: Button behavior ("default", "profile", "popup")
- `isDark`: Theme mode for styling fixes
- `variant`: Button style ("transparent", "colored")

### Supported Card Types
- `share-card`: Scholar analysis cards
- `share-card-github`: GitHub analysis cards  
- `share-card-compare`: Scholar comparison cards
- `share-card-github-compare`: GitHub comparison cards

## Configuration

### Environment Variables
The Cloudflare Worker requires these environment variables:
- `AWS_REGION`: AWS region for S3 bucket
- `AWS_ACCESS_KEY_ID`: AWS access key
- `AWS_SECRET_ACCESS_KEY`: AWS secret key
- `S3_BUCKET_NAME`: S3 bucket name for storing files

### CORS Configuration
S3 bucket must allow CORS from your domain:
```xml
<CORSConfiguration>
 <CORSRule>
   <AllowedOrigin>https://dinq.io</AllowedOrigin>
   <AllowedMethod>PUT</AllowedMethod>
   <AllowedMethod>POST</AllowedMethod>
   <AllowedHeader>*</AllowedHeader>
 </CORSRule>
</CORSConfiguration>
```

## API Reference

### Utility Functions

#### `getPresignedUrl(fileType)`
Gets presigned URL from Cloudflare Worker.
- **Parameters**: `fileType` - "image/png" or "text/html"
- **Returns**: `{uploadUrl, publicUrl}`

#### `uploadFileToS3(blob, fileType)`
Uploads file to S3 using presigned URL.
- **Parameters**: `blob` - File blob, `fileType` - MIME type
- **Returns**: Public URL of uploaded file

#### `generateRedirectHtml(imageUrl, pageUrl, title, description)`
Generates HTML redirect page with Open Graph tags.
- **Parameters**: Image URL, target page URL, title, description
- **Returns**: HTML string

#### `createShareableUrl(imageBlob, pageUrl, title, description)`
Complete sharing workflow function.
- **Parameters**: Image blob, target URL, title, description  
- **Returns**: Shareable URL

## Testing

Use the test page at `test-share-workflow.html` to validate:
1. API connection to Cloudflare Worker
2. Image upload to S3
3. HTML upload to S3
4. Complete sharing workflow

## Troubleshooting

### Common Issues

1. **CORS Errors**: Check S3 bucket CORS configuration
2. **Upload Failures**: Verify AWS credentials and permissions
3. **Image Generation Issues**: Check for external image CORS problems
4. **Styling Problems**: Verify theme detection and CSS fixes

### Error Messages
- "Failed to get presigned URL": Check Cloudflare Worker deployment
- "Failed to upload file to S3": Check AWS credentials and S3 permissions
- "Failed to create image blob": Check html2canvas compatibility

## Security Considerations

- AWS credentials are stored securely in Cloudflare Worker
- Presigned URLs have 5-minute expiration
- Files are uploaded directly from browser to S3
- No sensitive data passes through frontend

## Performance

- Image generation: ~500ms
- S3 upload: ~1-2 seconds per file
- Total workflow: ~3-5 seconds
- Generated files are cached by S3/CloudFront

## Future Enhancements

- Add support for other social platforms (LinkedIn, Facebook)
- Implement image optimization and compression
- Add analytics tracking for shared content
- Support for custom branding and themes
