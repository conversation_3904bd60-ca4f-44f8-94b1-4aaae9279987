<!-- components/BarChart/index.vue -->
<template>
  <div class="h-full">
    <div v-if="!hasActivityData" class="flex items-center justify-center h-full text-gray-500">
      No activity data available
    </div>
    <Bar v-else :data="chartData" :options="chartOptions" class="h-full"/>
  </div>
</template>

<script setup>
import { computed, onMounted, ref } from 'vue'
import { Bar } from 'vue-chartjs'
import {
  Chart as ChartJS,
  Title,
  Tooltip,
  Legend,
  BarElement,
  CategoryScale,
  LinearScale,
} from 'chart.js'

ChartJS.register(Title, Tooltip, Legend, BarElement, CategoryScale, LinearScale)

// Props for GitHub activity data
const props = defineProps({
  activityData: {
    type: Object,
    default: () => ({})
  }
})

// 检测深色模式
const isDark = ref(false)

onMounted(() => {
  isDark.value = document.documentElement.classList.contains('dark')
  const observer = new MutationObserver(() => {
    isDark.value = document.documentElement.classList.contains('dark')
  })
  observer.observe(document.documentElement, { attributes: true, attributeFilter: ['class'] })
})

// 检查是否有活动数据
const hasActivityData = computed(() => {
  return props.activityData && Object.keys(props.activityData).length > 0
})

// 生成最近31天的日期标签
const getLast31Days = () => {
  const dates = []
  const now = new Date()
  const nowTimestamp = now.getTime()
  
  for (let i = 30; i >= 0; i--) {
    // 使用时间戳计算，避免跨月份的日期bug
    const timestamp = nowTimestamp - i * 24 * 60 * 60 * 1000
    const date = new Date(timestamp)
    const dateStr = date.toISOString().split('T')[0] // YYYY-MM-DD format
    const label = `${date.getDate()}` // 只显示日期，不要月份
    dates.push({ label, dateStr })
  }
  return dates
}

// 从GitHub活动数据中提取数据
const extractActivityData = () => {
  const days = getLast31Days()
  const pullRequests = []
  const issues = []
  const contributions = []
  const comments = []

  days.forEach(({ dateStr }) => {
    const dayData = props.activityData[dateStr] || {
      pull_requests: 0,
      issues: 0,
      contributions: 0,
      comments: 0
    }
    
    pullRequests.push(dayData.pull_requests || 0)
    issues.push(dayData.issues || 0)
    contributions.push(dayData.contributions || 0)
    comments.push(dayData.comments || 0)
  })

  return {
    labels: days.map(d => d.label),
    pullRequests,
    issues,
    contributions,
    comments
  }
}

const chartData = computed(() => {
  if (!hasActivityData.value) {
    // 返回空数据结构，避免图表报错
    return {
      labels: [],
      datasets: []
    }
  }

  const data = extractActivityData()
  
  // 根据深色模式选择颜色
  const colors = isDark.value ? {
    prs: '#5A6275',
    issues: '#9183A5', 
    commits: '#654D43',
    comments: '#817968'
  } : {
    prs: '#7F95CE',
    issues: '#B89EDA',
    commits: '#CB7C5D',
    comments: '#F8E9C8'
  }
  
  return {
    labels: data.labels,
    datasets: [
      {
        label: 'PRs',
        backgroundColor: colors.prs,
        data: data.pullRequests,
        stack: 'activity'
      },
      {
        label: 'Issues',
        backgroundColor: colors.issues,
        data: data.issues,
        stack: 'activity'
      },
      {
        label: 'Commits',
        backgroundColor: colors.commits,
        data: data.contributions,
        stack: 'activity'
      },
      {
        label: 'Comments',
        backgroundColor: colors.comments,
        data: data.comments,
        stack: 'activity'
      }
    ]
  }
})

const chartOptions = computed(() => ({
  responsive: true,
  maintainAspectRatio: false, // Allow chart height to be controlled by container
  plugins: {
    legend: {
      position: 'bottom',
      labels: {
        usePointStyle: true,
        pointStyle: 'rectRounded', // ⬅️ 设置为圆角矩形
        padding: 35,
        boxHeight: 12,
        font: {
          family: 'Poppins',
          weight: 400,
          size: 16,
          lineHeight: 1.0
        }
      },
    },
    datalabels: {
    display: false, // ❌ 不显示柱上的值
    },
  },
  scales: {
    x: {
      stacked: true,
      grid: {
        display: false // 关闭 X 轴网格线
      },
      ticks: {
        maxRotation: 0,
        minRotation: 0,
        font: {
          family: 'Poppins',
          weight: 500,
          size: 12,
          
        }
      }
    },
    y: {
      stacked: true,
      beginAtZero: true,
      border: {
        color: isDark.value ? '#404040' : '#E5E5E5', // Y轴轴线颜色
      },
      grid: {
        display: true, // 显示 y 轴网格线
        color: isDark.value ? '#404040' : '#E5E5E5', // 深色模式使用 #404040，浅色模式使用 #E5E5E5
        lineWidth: 1, // 设置网格线宽度
        drawOnChartArea: true, // 只在图表区域内绘制网格线
        drawTicks: false, // 不绘制刻度线，避免超出
      },
      ticks: {
        maxTicksLimit: 6, // 限制最大刻度数量，减少密集度
        font: {
          family: 'Poppins',
          weight: 400,
          size: 12,
        },
        color: '#666', // 设置刻度标签颜色
        padding: 8 // 增加刻度标签与轴线的距离
      }
    }
  }
}))
</script>
