<template>
  <div ref="chartRef" class="w-full h-full" />
</template>

<script setup lang="ts">
  import * as echarts from 'echarts'
  import type { ConferenceDistribution } from '~/api/types'
  import { onMounted, ref, onBeforeUnmount, computed, watch } from 'vue'
  const props = defineProps<{
    conferenceDistribution: ConferenceDistribution
    topTierPapers?: number
  }>()
  const chartRef = ref<HTMLDivElement | null>(null)
  let chart: echarts.ECharts | null = null

  const lightColors = ['#7F95CE', '#D2CEC4', '#CB7C5D', '#F8E9C8']
  const darkColors = ['#5A6275', '#919191', '#654D43', '#817968']

  // 检测深色模式
  const isDark = ref(false)
  
  const colors = computed(() => isDark.value ? darkColors : lightColors)

  const convertToRawData = (data: ConferenceDistribution) => {
    return Object.entries(data).map(([name, value]) => ({
      name,
      value,
    }))
  }
  
  const rawData = convertToRawData(props.conferenceDistribution)
  
  // 使用传入的topTierPapers值，如果没有则计算conferenceDistribution的总和作为备用
  const totalTopTierPapers = computed(() => {
    return props.topTierPapers ?? rawData.reduce((sum, item) => sum + item.value, 0)
  })
  
  // 处理数据：按值从大到小排序，只取前4个
  const processedData = computed(() => {
    // 按值从大到小排序
    const sortedData = [...rawData].sort((a, b) => {
      if (b.value !== a.value) {
        return b.value - a.value
      }
      // 如果值相等，按名称字母顺序排序（确保一致性）
      return a.name.localeCompare(b.name)
    })
    
    // 只取前4个
    return sortedData.slice(0, 4)
  })
  
  const seriesData = computed(() => processedData.value.map((item, index) => ({
    ...item,
    label: {
      show: true,
      position: 'outside',
      distanceToLabelLine: 5,
      formatter: (params: any) => {
        const name = params.name
        const value = params.value
        const percent = params.percent
        return `{bar|}\n{name|${name}}\n{value|${value}} {percent|${percent}%}`
      },
      fontSize: 14,
      fontFamily: 'Poppins',
      color: isDark.value ? '#FFFFFF' : '#030229',
      backgroundColor: isDark.value ? '#2A2A2A' : '#FAF2EF',
      width: 100,
      height: 50,
      rich: {
        bar: {
          height: 4,
          width: '100%',
          backgroundColor: colors.value[index],
        },
        name: {
          fontSize: 11,
          fontFamily: 'Poppins',
          align: 'center',
          padding: [8, 8, 6, 8],
        },
        value: {
          fontSize: 11,
          fontFamily: 'Poppins',
          align: 'left',
          padding: [0, 8, 0, 8],
        },
        percent: {
          fontSize: 11,
          fontFamily: 'Poppins',
          align: 'right',
          padding: [0, 8, 0, 8],
        },
      },
    },
  })))

  const option = computed(() => ({
    color: colors.value,
    backgroundColor: 'transparent',
    grid: {
      top: 20,
      right: 20,
      bottom: 20,
      left: 20,
      containLabel: true,
    },
    legend: {
      show: false,
    },
    series: [
      {
        name: 'Publications',
        type: 'pie',
        radius: ['45%', '60%'],
        center: ['50%', '50%'],
        avoidLabelOverlap: true,
        labelLine: {
          show: true,
          length: 15,
          length2: 10,
          smooth: true,
          lineStyle: {
            width: 1,
            color: isDark.value ? '#666' : '#ccc',
          },
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 16,
            fontWeight: 'bold',
          },
        },
        data: seriesData.value,
      },
    ],
    graphic: [
      {
        type: 'text',
        left: 'center',
        top: '42.5%',
        style: {
          text: totalTopTierPapers.value.toString(),
          textAlign: 'center',
          fill: isDark.value ? '#FFFFFF' : '#030229',
          fontSize: 27,
          fontWeight: 'bold',
          fontFamily: 'Poppins',
        },
      },
      {
        type: 'text',
        left: 'center',
        top: '57.5%',
        style: {
          text: 'Top Tier',
          textAlign: 'center',
          fill: isDark.value ? '#A0A0A0' : 'rgba(3, 2, 41, 0.7)',
          fontSize: 12,
          fontWeight: '500',
          fontFamily: 'Poppins',
        },
      },
    ],
  }))

  // 检测深色模式变化
  const updateDarkMode = () => {
    isDark.value = document.documentElement.classList.contains('dark')
  }

  const updateChart = () => {
    if (chart) {
      chart.setOption(option.value, true)
    }
  }

  onMounted(() => {
    // 初始化深色模式检测
    updateDarkMode()
    
    // 监听深色模式变化
    const observer = new MutationObserver(() => {
      updateDarkMode()
      updateChart() // 深色模式变化时更新图表
    })
    observer.observe(document.documentElement, { 
      attributes: true, 
      attributeFilter: ['class'] 
    })
    
    if (chartRef.value) {
      chart = echarts.init(chartRef.value)
      chart.setOption(option.value)
    }
  })

  // Watch for dark mode changes
  watch(isDark, () => {
    updateChart()
  })

  onBeforeUnmount(() => {
    chart?.dispose()
  })
</script>

<style scoped>
  div {
    width: 100%;
    height: 280px;
    background-color: transparent;
  }
</style>
