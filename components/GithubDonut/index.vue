<template>
  <div ref="chartRef" class="w-full h-full">
    <div v-if="!hasData" class="flex items-center justify-center h-full text-gray-500">
      No language data available
    </div>
  </div>
</template>

<script setup lang="ts">
import * as echarts from 'echarts'
import { onMounted, ref, onBeforeUnmount, computed, watch } from 'vue'

// 格式化数字，超过1M时显示为M单位
const formatNumber = (num: number): string => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M'
  }
  return num.toLocaleString()
}

// Props for GitHub languages data
const props = defineProps({
  languages: {
    type: Object,
    default: () => ({})
  },
  total: {
    type: Number,
    default: null
  },
  compact: {
    type: Boolean,
    default: false
  }
})

// 自定义配色
const lightColors = ['#7F95CE', '#F8E9C8', '#CB7C5D', '#D2CEC4', '#B89EDA', '#A8C8A8']
const darkColors = ['#5A6275', '#817968', '#654D43', '#919191', '#6B5B95', '#5A7A5A']

// 图表 DOM
const chartRef = ref<HTMLDivElement | null>(null)
let chart: echarts.ECharts | null = null
let isComponentMounted = false

// 检测深色模式
const isDark = ref(false)

// 根据深色模式选择颜色
const colors = computed(() => isDark.value ? darkColors : lightColors)

// 验证数值是否有效
const isValidNumber = (value: any): boolean => {
  return typeof value === 'number' && !isNaN(value) && isFinite(value) && value >= 0
}

// 检查是否有数据
const hasData = computed(() => {
  const languageEntries = Object.entries(props.languages || {})
  return languageEntries.length > 0 && languageEntries.some(([, value]) => isValidNumber(value) && value > 0)
})

// 处理语言数据
const processedData = computed(() => {
  const languageEntries = Object.entries(props.languages || {})
  
  if (languageEntries.length === 0) {
    return []
  }

  // 验证并过滤有效数据
  const validLanguages = languageEntries.filter(([, value]) => isValidNumber(value) && value > 0)
  
  if (validLanguages.length === 0) {
    return []
  }

  // Sort by value and take top languages
  const sortedLanguages = validLanguages.sort(([,a], [,b]) => b - a)

  // Take top 5 languages
  const topLanguages = sortedLanguages.slice(0, 5)
  
  // If there are more than 5 languages, group the rest as "Other"
  const remainingLanguages = sortedLanguages.slice(5)
  const otherTotal = remainingLanguages.reduce((sum, [, value]) => sum + value, 0)

  const result = topLanguages.map(([name, value]) => ({ name, value }))
  
  if (otherTotal > 0) {
    result.push({ name: 'Other', value: otherTotal })
  }

  return result
})

// 总数 - 优先使用传入的 total，否则计算 languages 的总和
const total = computed(() => {
  if (props.total !== null && props.total !== undefined) {
    return props.total
  }
  return processedData.value.reduce((sum, item) => sum + item.value, 0)
})

// 构造 series 数据，含 label 样式
const seriesData = computed(() => processedData.value.map((item, index) => ({
  ...item,
  label: {
    show: true, // 始终显示外部标签
    position: 'outside',
    formatter: (params: any) => {
      const { name, value, percent } = params
      return `{bar|}\n{name|${name}}\n{value|${formatNumber(value)}}{percent|${percent}%}`
    },
    backgroundColor: isDark.value ? '#2A2A2A' : '#FAF2EF',
    borderRadius: 0,
    padding: [8, 10, 8, 10], // 增加padding使标签更美观
    fontSize: 12,
    fontFamily: 'Poppins',
    color: isDark.value ? '#FFFFFF' : '#030229',
    width: 100, // 限制标签最大宽度
          rich: {
        bar: {
          height: 5,
          width: '100%',
          backgroundColor: colors.value[index % colors.value.length],
        },
      name: {
        fontSize: 14, // 增大字号
        fontFamily: 'Poppins',
        align: 'center', // 居中
        padding: [4, 8, 4, 8], // 调整padding
        lineHeight: 18, // 增加行高
      },
      value: {
        fontSize: 12,
        fontFamily: 'Poppins',
        align: 'left', // 左对齐
        padding: [2, 8, 4, 0], // 调整padding
        lineHeight: 16,
      },
      percent: {
        fontSize: 12,
        fontFamily: 'Poppins',
        align: 'right', // 右对齐
        padding: [2, 0, 4, 8], // 调整padding
        lineHeight: 16,
      },
    },
  },
})))

const option = computed(() => ({
  color: colors.value,
  backgroundColor: 'transparent',
  legend: {
    show: !props.compact, // 根据compact属性控制图例显示：compact模式（分享卡片）不显示图例，正常模式显示图例
    bottom: 0,
    itemWidth: 16, // 增大图例宽度
    itemHeight: 16, // 增大图例高度
    icon: 'roundRect',
    textStyle: {
      fontSize: 12, // 增大图例文字
      fontFamily: 'Poppins',
      color: isDark.value ? '#FFFFFF' : '#030229',
    },
  },
  series: [
    {
      type: 'pie',
      radius: ['45%', '65%'], // 增大圆圈大小
      avoidLabelOverlap: false,
      labelLine: {
        show: true,
        length: 12, // 缩短引导线第一段
        length2: 8,  // 缩短引导线第二段
        lineStyle: {
          color: isDark.value ? '#666' : '#ccc',
          width: 1,
        },
        smooth: true,
      },
      data: seriesData.value,
    },
  ],
  graphic: props.compact ? [
    {
      type: 'text',
      left: 'center',
      top: '42%',
      style: {
        text: formatNumber(total.value), // 显示总代码数
        textAlign: 'center',
        fill: isDark.value ? '#FFFFFF' : '#030229',
        fontSize: 18,
        fontWeight: 600,
        fontFamily: 'Poppins',
      },
    },
    {
      type: 'text',
      left: 'center',
      top: '52%',
      style: {
        text: 'Total Code',
        textAlign: 'center',
        fill: isDark.value ? '#A0A0A0' : '#969696',
        fontSize: 11,
        fontWeight: 500,
        fontFamily: 'Poppins',
      },
    },
  ] : [
    {
      type: 'text',
      left: 'center',
      top: '42%',
      style: {
        text: formatNumber(total.value), // 格式化总数
        textAlign: 'center',
        fill: isDark.value ? '#FFFFFF' : '#030229',
        fontSize: 22,
        fontWeight: 600,
        fontFamily: 'Poppins',
      },
    },
    {
      type: 'text',
      left: 'center',
      top: '52%',
      style: {
        text: 'Total Code',
        textAlign: 'center',
        fill: isDark.value ? '#A0A0A0' : '#030229',
        fontSize: 11,
        fontFamily: 'Poppins',
      },
    },
  ],
}))

// 检测深色模式变化
const updateDarkMode = () => {
  isDark.value = document.documentElement.classList.contains('dark')
}

const updateChart = () => {
  if (chart && hasData.value && isComponentMounted) {
    chart.setOption(option.value, true)
  }
}

onMounted(() => {
  isComponentMounted = true
  
  // 初始化深色模式检测
  updateDarkMode()
  
  // 监听深色模式变化
  const observer = new MutationObserver(() => {
    updateDarkMode()
    updateChart() // 深色模式变化时更新图表
  })
  observer.observe(document.documentElement, { 
    attributes: true, 
    attributeFilter: ['class'] 
  })
  
  if (chartRef.value && hasData.value) {
    chart = echarts.init(chartRef.value)
    updateChart()
  }
})

// Watch for data changes
watch(() => props.languages, () => {
  if (!isComponentMounted) return
  
  if (hasData.value) {
    if (!chart && chartRef.value) {
      chart = echarts.init(chartRef.value)
    }
    updateChart()
  } else if (chart) {
    chart.dispose()
    chart = null
  }
}, { deep: true })

// Watch for dark mode changes
watch(isDark, () => {
  updateChart()
})

onBeforeUnmount(() => {
  isComponentMounted = false
  if (chart) {
    chart.dispose()
    chart = null
  }
})
</script>

<style scoped>
div {
  width: 100%;
  height: 100%;
  min-height: 180px;
}
</style>
