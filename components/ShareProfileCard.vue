<template>
  <div ref="rootRef" class="share-profile-card bg-white rounded-[32px] shadow-xl px-10 pt-10 pb-8 w-[1300px] mx-auto flex flex-col gap-7 relative overflow-hidden">
    <!-- 背景图 -->
    <div class="absolute right-0 top-0 w-[800px] h-full bg-[url('/image/graphbg.png')] bg-no-repeat bg-cover bg-right-top opacity-100"></div>

    <!-- 内容容器 -->
    <div class="w-[900px] relative z-10">
      <!-- 顶部个人信息 -->
      <div class="flex items-center gap-6 mb-2">
        <Avatar :src="profile.avatar" :size="72" />
        <div class="flex flex-col gap-1">
          <div class="flex items-center gap-2">
            <span class="text-2xl font-extrabold text-gray-900">{{ profile.name }}</span>
            <span class="text-xs px-2 py-0.5 rounded-full bg-[#F8E9C8] text-[#A48A77] font-semibold">{{ profile.identity || 'OpenAi-Researcher' }}</span>
          </div>
          <div class="flex items-center gap-6 mt-1">
            <span class="text-lg font-bold text-gray-900">{{ profile.conferencePaper }}</span>
            <span class="text-xs text-gray-500">Conference Paper</span>
            <span class="text-lg font-bold text-gray-900 ml-6">{{ profile.citations }}</span>
            <span class="text-xs text-gray-500">Citations</span>
          </div>
        </div>
      </div>

      <!-- 主体内容区块 -->
      <div class="flex gap-7 mb-7">
        <!-- 左侧 Roast 区块 -->
        <div class="flex-1 bg-[#FFF7F0] rounded-2xl p-7 flex flex-col gap-2 justify-between">
          <div class="flex items-center gap-3 mb-2">
            <SvgIcon name="mail" className="text-[#A48A77] w-6 h-6" />
            <span class="font-bold text-lg text-gray-900">Roast</span>
          </div>
          <div class="text-gray-700 text-base leading-relaxed flex-1 flex items-center">{{ roastText }}</div>
        </div>
        <!-- 右侧 Research Character 区块 -->
        <div class="flex-1 bg-[#FAF8F5] rounded-2xl p-7 flex flex-col gap-4 justify-between">
          <div class="flex items-center gap-3 mb-2">
            <SvgIcon name="analytics" className="text-[#A48A77] w-6 h-6" />
            <span class="font-bold text-lg text-gray-900">Research Character</span>
          </div>
          <div class="flex flex-col gap-3 mt-2 flex-1 justify-center">
            <div>
              <div class="flex justify-between text-xs text-gray-500 mb-1">
                <span>Theoretical Research</span>
                <span>Applied Research</span>
              </div>
              <Progress :percentage="characterStats.theoryVsPractice * 10" />
            </div>
            <div>
              <div class="flex justify-between text-xs text-gray-500 mb-1">
                <span>Academic Depth</span>
                <span>Academic Breadth</span>
              </div>
              <Progress :percentage="characterStats.depthVsBreadth * 10" />
            </div>
            <div>
              <div class="flex justify-between text-xs text-gray-500 mb-1">
                <span>Independent Research</span>
                <span>Team Collaboration</span>
              </div>
              <Progress :percentage="characterStats.soloVsTeamwork * 10" />
            </div>
          </div>
        </div>
      </div>
      <!-- Role Model 区块整行展示 -->
      <div class="bg-[#FAF8F5] rounded-2xl p-7 flex flex-col gap-4 w-full">
        <div class="flex items-center gap-3 mb-2">
          <SvgIcon name="settings" className="text-[#A48A77] w-6 h-6" />
          <span class="font-bold text-lg text-gray-900">Role Model</span>
        </div>
        <div class="flex items-center gap-6 mb-2 flex-wrap">
          <Avatar :src="roleModel.avatar" :size="64" />
          <div class="flex flex-col min-w-[120px]">
            <span class="font-bold text-xl text-gray-900">{{ roleModel.name }}</span>
            <span class="text-xs text-gray-500">{{ roleModel.identity }}</span>
          </div>
          <span class="text-xs text-gray-500">Academic citation: <span class="font-bold text-gray-900">{{ roleModel.citations }}</span></span>
        </div>
        <div class="text-xs text-gray-700 bg-[#F8E9C8] rounded px-2 py-1 mb-1 max-w-2xl">{{ roleModel.achievement }}</div>
        <div class="text-sm text-gray-900 max-w-3xl">{{ roleModel.description }}</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import Avatar from '@/components/Avatar/index.vue'
import DonutChart from '@/components/DonutChart/index.vue'
import Progress from '@/components/Progress/index.vue'
import SvgIcon from '@/components/SvgIcon/index.vue'
import { computed, ref, onMounted } from 'vue'

const props = defineProps<{ dataBlocks: any }>()

// 顶部信息
const profile = computed(() => ({
  avatar: props.dataBlocks?.roleModel?.photoUrl || '/image/avator.png',
  name: props.dataBlocks?.roleModel?.name || 'Name',
  identity: 'OpenAi-Researcher',
  conferencePaper: props.dataBlocks?.publicationStats?.totalPapers || 0,
  citations: props.dataBlocks?.publicationStats?.totalCitations || 0
}))

// Papers 区块数据
const papersStats = computed(() => ({
  firstAuthorPapers: props.dataBlocks?.publicationInsight?.firstAuthorPapers || 0,
  totalPapers: props.dataBlocks?.publicationStats?.totalPapers || 0,
  citationRate: props.dataBlocks?.publicationStats?.citationRate || '0%',
  topTierPapers: props.dataBlocks?.publicationInsight?.topTierPapers || 0
}))
const conferenceDistribution = computed(() => props.dataBlocks?.publicationInsight?.conferenceDistribution || {})

// Research Character 区块数据
const characterStats = computed(() => ({
  theoryVsPractice: props.dataBlocks?.researcherCharacter?.theoryVsPractice || 0,
  depthVsBreadth: props.dataBlocks?.researcherCharacter?.depthVsBreadth || 0,
  soloVsTeamwork: props.dataBlocks?.researcherCharacter?.soloVsTeamwork || 0
}))

// Roast 区块
const roastText = computed(() => props.dataBlocks?.criticalReview?.evaluation || '')

// Role Model 区块
const roleModel = computed(() => ({
  avatar: props.dataBlocks?.roleModel?.photoUrl || '/image/avator.png',
  name: props.dataBlocks?.roleModel?.name || '',
  identity: 'OpenAi-Researcher',
  citations: props.dataBlocks?.roleModel?.citations || 0,
  achievement: props.dataBlocks?.roleModel?.achievement || '',
  description: props.dataBlocks?.roleModel?.similarityReason || ''
}))

// 暴露根节点ref
const rootRef = ref<HTMLElement | null>(null)

// 替换 DonutChart canvas 为 img
let donutCanvas: HTMLCanvasElement | null = null
let donutImg: HTMLImageElement | null = null
function replaceDonutCanvasWithImg() {
  if (!rootRef.value) return;
  donutCanvas = rootRef.value.querySelector('canvas');
  if (donutCanvas) {
    const dataUrl = donutCanvas.toDataURL('image/png');
    donutImg = document.createElement('img');
    donutImg.src = dataUrl;
    donutImg.width = donutCanvas.width;
    donutImg.height = donutCanvas.height;
    donutImg.style.display = 'block';
    donutCanvas.parentNode?.replaceChild(donutImg, donutCanvas);
  }
}

// 还原 img 为 canvas
function restoreDonutCanvas() {
  if (donutImg && donutCanvas && donutImg.parentNode) {
    donutImg.parentNode.replaceChild(donutCanvas, donutImg);
    donutImg = null;
    donutCanvas = null;
  }
}

defineExpose({ rootRef, replaceDonutCanvasWithImg, restoreDonutCanvas });
</script>

<style scoped>
.share-profile-card {
  font-family: 'Inter', 'Helvetica Neue', Arial, sans-serif;
  box-shadow: 0 8px 32px 0 rgba(44, 39, 56, 0.10);
}
</style> 