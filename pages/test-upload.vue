<template>
  <div class="min-h-screen bg-gray-50 py-8">
    <div class="max-w-4xl mx-auto px-4">
      <h1 class="text-3xl font-bold text-gray-900 mb-8">🧪 S3 Upload Test</h1>
      
      <!-- Test API Connection -->
      <div class="bg-white rounded-lg shadow p-6 mb-6">
        <h3 class="text-lg font-semibold mb-4">1. Test API Connection</h3>
        <p class="text-gray-600 mb-4">Test connection to Cloudflare Worker API</p>
        <button 
          @click="testApiConnection"
          :disabled="apiTesting"
          class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 disabled:opacity-50"
        >
          {{ apiTesting ? 'Testing...' : 'Test API Connection' }}
        </button>
        <div v-if="apiResult" class="mt-4 p-4 rounded" :class="apiResult.success ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'">
          <pre class="whitespace-pre-wrap text-sm">{{ apiResult.message }}</pre>
        </div>
      </div>

      <!-- Test Image Upload -->
      <div class="bg-white rounded-lg shadow p-6 mb-6">
        <h3 class="text-lg font-semibold mb-4">2. Test Image Upload</h3>
        <p class="text-gray-600 mb-4">Generate a test image and upload to S3</p>
        <div 
          ref="testCard" 
          class="w-96 h-48 bg-gradient-to-br from-blue-500 to-purple-600 text-white flex items-center justify-center rounded-lg mb-4 text-xl font-bold"
        >
          DINQ Test Card
        </div>
        <button 
          @click="testImageUpload"
          :disabled="imageTesting"
          class="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600 disabled:opacity-50"
        >
          {{ imageTesting ? 'Uploading...' : 'Generate & Upload Image' }}
        </button>
        <div v-if="imageResult" class="mt-4 p-4 rounded" :class="imageResult.success ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'">
          <pre class="whitespace-pre-wrap text-sm">{{ imageResult.message }}</pre>
        </div>
      </div>

      <!-- Test Upload Debug -->
      <div class="bg-white rounded-lg shadow p-6 mb-6">
        <h3 class="text-lg font-semibold mb-4">3. Debug Upload Issue</h3>
        <p class="text-gray-600 mb-4">Debug the upload cancellation issue with detailed logging</p>
        <button 
          @click="testUploadDebug"
          :disabled="debugTesting"
          class="bg-yellow-500 text-white px-4 py-2 rounded hover:bg-yellow-600 disabled:opacity-50"
        >
          {{ debugTesting ? 'Debugging...' : 'Debug Upload Issue' }}
        </button>
        <div v-if="debugResult" class="mt-4 p-4 rounded" :class="debugResult.success ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'">
          <pre class="whitespace-pre-wrap text-sm">{{ debugResult.message }}</pre>
        </div>
      </div>

      <!-- Test New Share URL Generation -->
      <div class="bg-white rounded-lg shadow p-6 mb-6">
        <h3 class="text-lg font-semibold mb-4">4. Test New Share URL Generation</h3>
        <p class="text-gray-600 mb-4">Test the new file naming and beautiful URL generation</p>
        <div class="space-y-4 mb-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Page Type:</label>
            <select v-model="testPageType" class="border border-gray-300 rounded px-3 py-2 w-full">
              <option value="github">GitHub</option>
              <option value="scholar">Scholar</option>
              <option value="github-compare">GitHub Compare</option>
              <option value="scholar-compare">Scholar Compare</option>
            </select>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Identifiers (comma-separated):</label>
            <input v-model="testIdentifiers" type="text" placeholder="e.g., username1,username2" class="border border-gray-300 rounded px-3 py-2 w-full">
          </div>
        </div>
        <button
          @click="testShareUrlGeneration"
          :disabled="urlTesting"
          class="bg-indigo-500 text-white px-4 py-2 rounded hover:bg-indigo-600 disabled:opacity-50"
        >
          {{ urlTesting ? 'Testing...' : 'Test URL Generation' }}
        </button>
        <div v-if="urlResult" class="mt-4 p-4 rounded" :class="urlResult.success ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'">
          <pre class="whitespace-pre-wrap text-sm">{{ urlResult.message }}</pre>
        </div>
      </div>

      <!-- Test Complete Workflow -->
      <div class="bg-white rounded-lg shadow p-6">
        <h3 class="text-lg font-semibold mb-4">5. Test Complete Workflow</h3>
        <p class="text-gray-600 mb-4">Test the complete sharing workflow (image + HTML)</p>
        <button 
          @click="testCompleteWorkflow"
          :disabled="workflowTesting"
          class="bg-purple-500 text-white px-4 py-2 rounded hover:bg-purple-600 disabled:opacity-50"
        >
          {{ workflowTesting ? 'Testing...' : 'Test Complete Workflow' }}
        </button>
        <div v-if="workflowResult" class="mt-4 p-4 rounded" :class="workflowResult.success ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'">
          <pre class="whitespace-pre-wrap text-sm">{{ workflowResult.message }}</pre>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import html2canvas from 'html2canvas-pro'
import { getPresignedUrl, uploadFileToS3, createShareableUrl, generateFileName, generateShareUrl, cleanIdentifier } from '~/utils'

// 页面标题
useHead({
  title: 'S3 Upload Test - DINQ'
})

// 响应式状态
const apiTesting = ref(false)
const imageTesting = ref(false)
const debugTesting = ref(false)
const urlTesting = ref(false)
const workflowTesting = ref(false)

const apiResult = ref<{success: boolean, message: string} | null>(null)
const imageResult = ref<{success: boolean, message: string} | null>(null)
const debugResult = ref<{success: boolean, message: string} | null>(null)
const urlResult = ref<{success: boolean, message: string} | null>(null)
const workflowResult = ref<{success: boolean, message: string} | null>(null)

const testCard = ref<HTMLElement>()

// 新的测试参数
const testPageType = ref<'github' | 'scholar' | 'github-compare' | 'scholar-compare'>('github')
const testIdentifiers = ref('testuser')

// 测试 API 连接
const testApiConnection = async () => {
  apiTesting.value = true
  apiResult.value = null
  
  try {
    console.log('=== API CONNECTION TEST START ===')
    const response = await getPresignedUrl('image/png')
    console.log('=== API CONNECTION TEST SUCCESS ===')
    
    apiResult.value = {
      success: true,
      message: `✅ API Connection Successful!\n\nResponse:\n${JSON.stringify(response, null, 2)}`
    }
  } catch (error: any) {
    console.error('=== API CONNECTION TEST FAILED ===')
    console.error(error)
    
    apiResult.value = {
      success: false,
      message: `❌ API Connection Failed!\n\nError: ${error.message}`
    }
  } finally {
    apiTesting.value = false
  }
}

// 测试图片上传
const testImageUpload = async () => {
  imageTesting.value = true
  imageResult.value = null
  
  try {
    console.log('=== IMAGE UPLOAD TEST START ===')
    
    if (!testCard.value) {
      throw new Error('Test card element not found')
    }
    
    // 生成图片
    const canvas = await html2canvas(testCard.value, {
      backgroundColor: '#ffffff',
      scale: 2,
    })
    
    // 转换为 blob
    const blob = await new Promise<Blob>((resolve) => {
      canvas.toBlob((blob) => resolve(blob!), 'image/png', 1.0)
    })
    
    console.log('Image generated, blob size:', blob.size)
    
    // 上传到 S3
    const imageUrl = await uploadFileToS3(blob, 'image/png')
    
    console.log('=== IMAGE UPLOAD TEST SUCCESS ===')
    
    imageResult.value = {
      success: true,
      message: `✅ Image Upload Successful!\n\nImage URL: ${imageUrl}\n\nYou can open this URL to view the uploaded image.`
    }
  } catch (error: any) {
    console.error('=== IMAGE UPLOAD TEST FAILED ===')
    console.error(error)
    
    imageResult.value = {
      success: false,
      message: `❌ Image Upload Failed!\n\nError: ${error.message}`
    }
  } finally {
    imageTesting.value = false
  }
}

// 调试上传问题
const testUploadDebug = async () => {
  debugTesting.value = true
  debugResult.value = null
  
  try {
    console.log('=== UPLOAD DEBUG TEST START ===')
    
    // 创建简单的测试 blob
    const testData = 'Hello, this is a test file for debugging upload issues.'
    const blob = new Blob([testData], { type: 'text/plain' })
    
    console.log('Test blob created:', blob.size, 'bytes')
    
    // 获取预签名 URL
    console.log('Step 1: Getting presigned URL...')
    const { uploadUrl, publicUrl } = await getPresignedUrl('text/html')
    
    console.log('Step 2: Attempting upload...')
    console.log('Upload URL:', uploadUrl)
    
    // 创建 AbortController 检测请求是否被取消
    const controller = new AbortController()
    const timeoutId = setTimeout(() => {
      console.log('Upload timeout after 30 seconds')
      controller.abort()
    }, 30000)
    
    // 监控上传请求
    const uploadResponse = await fetch(uploadUrl, {
      method: 'PUT',
      headers: {
        'Content-Type': 'text/html',
      },
      body: blob,
      signal: controller.signal
    })
    
    clearTimeout(timeoutId)
    
    console.log('Upload completed with status:', uploadResponse.status)
    console.log('Response headers:', Object.fromEntries(uploadResponse.headers.entries()))
    
    if (!uploadResponse.ok) {
      const errorText = await uploadResponse.text().catch(() => 'Unable to read error response')
      throw new Error(`Upload failed: ${uploadResponse.status} - ${errorText}`)
    }
    
    console.log('=== UPLOAD DEBUG TEST SUCCESS ===')
    
    debugResult.value = {
      success: true,
      message: `✅ Upload Debug Successful!\n\nPublic URL: ${publicUrl}\n\nCheck console for detailed logs.`
    }
    
  } catch (error: any) {
    console.error('=== UPLOAD DEBUG TEST FAILED ===')
    console.error('Error details:', error)
    
    if (error.name === 'AbortError') {
      debugResult.value = {
        success: false,
        message: `❌ Upload was aborted/cancelled!\n\nThis suggests the request is being cancelled by the browser or network.\n\nError: ${error.message}`
      }
    } else {
      debugResult.value = {
        success: false,
        message: `❌ Upload Debug Failed!\n\nError: ${error.message}\n\nCheck console for detailed logs.`
      }
    }
  } finally {
    debugTesting.value = false
  }
}

// 测试完整工作流
const testCompleteWorkflow = async () => {
  workflowTesting.value = true
  workflowResult.value = null
  
  try {
    console.log('=== COMPLETE WORKFLOW TEST START ===')
    
    if (!testCard.value) {
      throw new Error('Test card element not found')
    }
    
    // 生成图片
    const canvas = await html2canvas(testCard.value, {
      backgroundColor: '#ffffff',
      scale: 2,
    })
    
    // 转换为 blob
    const blob = await new Promise<Blob>((resolve) => {
      canvas.toBlob((blob) => resolve(blob!), 'image/png', 1.0)
    })
    
    // 创建分享链接
    const shareableUrl = await createShareableUrl(
      blob,
      'https://dinq.me/test',
      'DINQ Test Share',
      'Testing the DINQ sharing workflow'
    )
    
    console.log('=== COMPLETE WORKFLOW TEST SUCCESS ===')
    
    workflowResult.value = {
      success: true,
      message: `✅ Complete Workflow Successful!\n\nShareable URL: ${shareableUrl}\n\nThis URL contains:\n- Open Graph meta tags\n- Twitter Card meta tags\n- Auto-redirect to target page\n\nYou can open this URL to test the sharing functionality.`
    }
  } catch (error: any) {
    console.error('=== COMPLETE WORKFLOW TEST FAILED ===')
    console.error(error)

    workflowResult.value = {
      success: false,
      message: `❌ Complete Workflow Failed!\n\nError: ${error.message}`
    }
  } finally {
    workflowTesting.value = false
  }
}

// 测试新的分享URL生成功能
const testShareUrlGeneration = async () => {
  urlTesting.value = true
  urlResult.value = null

  try {
    console.log('=== SHARE URL GENERATION TEST START ===')

    const identifiers = testIdentifiers.value.split(',').map(id => id.trim()).filter(Boolean)

    if (identifiers.length === 0) {
      throw new Error('Please provide at least one identifier')
    }

    // 测试文件名生成
    const fileName = generateFileName(testPageType.value, ...identifiers)
    console.log('Generated fileName:', fileName)

    // 测试分享URL生成
    const shareUrl = generateShareUrl(testPageType.value, ...identifiers)
    console.log('Generated shareUrl:', shareUrl)

    // 测试清理标识符
    const cleanedIds = identifiers.map(id => cleanIdentifier(id))
    console.log('Cleaned identifiers:', cleanedIds)

    // 测试完整的分享流程（使用测试卡片）
    if (!testCard.value) {
      throw new Error('Test card element not found')
    }

    // 生成图片
    const canvas = await html2canvas(testCard.value, {
      backgroundColor: '#ffffff',
      scale: 2,
    })

    // 转换为 blob
    const blob = await new Promise<Blob>((resolve) => {
      canvas.toBlob((blob) => resolve(blob!), 'image/png', 1.0)
    })

    // 使用新的文件名创建分享链接
    const finalShareUrl = await createShareableUrl(
      blob,
      'https://dinq.io/test',
      'DINQ Test Share with New Naming',
      'Testing the new file naming and beautiful URL system',
      fileName,
      shareUrl
    )

    console.log('=== SHARE URL GENERATION TEST SUCCESS ===')

    urlResult.value = {
      success: true,
      message: `✅ Share URL Generation Successful!\n\nPage Type: ${testPageType.value}\nIdentifiers: ${identifiers.join(', ')}\nCleaned IDs: ${cleanedIds.join(', ')}\n\nGenerated File Name: ${fileName}\nGenerated Share URL: ${shareUrl}\nFinal Share URL: ${finalShareUrl}\n\nThe system is working correctly! 🎉`
    }
  } catch (error: any) {
    console.error('=== SHARE URL GENERATION TEST FAILED ===')
    console.error(error)

    urlResult.value = {
      success: false,
      message: `❌ Share URL Generation Failed!\n\nError: ${error.message}\n\nStack: ${error.stack}`
    }
  } finally {
    urlTesting.value = false
  }
}
</script>
