<template>
  <div>
    <!-- 重定向页面，不显示任何内容 -->
  </div>
</template>

<script setup lang="ts">
  // 重定向到新的 scholar 路由
  const route = useRoute()

  // 获取查询参数并重定向
  const query = route.query.query as string

  if (query) {
    // 如果有 query 参数，重定向到 /scholar?user=
    await navigateTo(`/scholar?user=${encodeURIComponent(query)}`, { replace: true })
  } else {
    // 如果没有参数，重定向到分析页面
    await navigateTo('/analysis', { replace: true })
  }
</script>