<template>
  <div>
    <!-- 重定向页面，不显示任何内容 -->
  </div>
</template>

<script setup lang="ts">
  // 重定向到新的 scholar_compare 路由
  const route = useRoute()
  
  // 获取查询参数并重定向
  const researcher1 = route.query.researcher1 as string
  const researcher2 = route.query.researcher2 as string
  
  if (researcher1 && researcher2) {
    // 如果有 researcher1 和 researcher2 参数，重定向到 /scholar_compare?user1=&user2=
    await navigateTo(`/scholar_compare?user1=${encodeURIComponent(researcher1)}&user2=${encodeURIComponent(researcher2)}`, { replace: true })
  } else {
    // 如果没有参数，重定向到分析页面
    await navigateTo('/analysis', { replace: true })
  }
</script>
