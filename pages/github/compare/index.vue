<template>
  <div>
    <!-- 重定向页面，不显示任何内容 -->
  </div>
</template>

<script setup lang="ts">
  // 重定向到新的 github_compare 路由
  const route = useRoute()

  // 获取查询参数并重定向
  const user1 = route.query.user1 as string
  const user2 = route.query.user2 as string

  if (user1 && user2) {
    // 如果有 user1 和 user2 参数，重定向到 /github_compare?user1=&user2=
    await navigateTo(`/github_compare?user1=${encodeURIComponent(user1)}&user2=${encodeURIComponent(user2)}`, { replace: true })
  } else {
    // 如果没有参数，重定向到分析页面
    await navigateTo('/analysis', { replace: true })
  }
</script>